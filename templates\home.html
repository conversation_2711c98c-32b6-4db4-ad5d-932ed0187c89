<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home</title>
    <script defer src="../static/home_js.js" type="module"></script>
    <script defer src="../static/js/predictions.js"></script>
    <link rel="stylesheet" href="../static/home_style.css">
</head>

<body>
    <h1 class="title">HedgePro</h1>
    <form action="/" method="POST">
        <div class="input_class">
            <input type="text" name="player_name" placeholder="Player Name" 
            id="player_name_input" autocomplete="off">
            <ul class="suggestions">
            </ul>
        </div>
    </form>

    <!-- Remove the refresh-container div that contains the refresh button -->

    <div class="rankedPlayers">Ranked Players</div>
    <div class="tab">
        <div class="tabs">
            <div class="prev">Previous Day's Matches</div>
            <div class="today">Today's Matches</div>
            <div class="next">Next Day's Matches</div>
        </div>
    
        <div class="genderTabs">
            <div class="active">ATP( SINGLES )</div>
            <div class="">WTA( SINGLES )</div>
        </div>
    </div>

    <div class="prevMatches matches_container men">
        {% if prev_rows | length == 0 %}
        <div>No data found</div>
        {% endif %}
        {% for row in prev_rows %}
        
        {% if row[-1] == "header" %}
        
        <div class="header">
            {% for i in range(11) %}
            
            <div class="col">{{row[i]}}</div>
        
            {% endfor %}
        </div>
        
        {% endif %}
        
        {% if row[-1] != "header" %}
        
        <div class="match-prediction-container">
            <div class="row">
                <div class="time">{{row[0][0]}}</div>
                <div class="players">
                    <div class="player1">
                        {% for j in range(1,9) %}
                        <div>{{row[0][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[0] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="player2">
                        {% for j in range(1,9) %}
                        <div>{{row[1][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[1] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Extract h and a values with a simpler approach -->
                {% set h_value = row[0][-2] %}
                {% set a_value = row[0][-1] %}
                
                <!-- Check if we have prediction data -->
                {% set has_prediction = false %}
                {% set prediction_data = None %}
                
                {% if row[0][-1] is string and "prediction" in row[0][-1].split(":") %}
                    {% set has_prediction = true %}
                    {% set prediction_text = row[0][-1][11:] %}
                    {% set prediction_data = prediction_text | fromjson %}
                {% endif %}
                
                <!-- Set h and a values from the row data -->
                {% if has_prediction == true %}
                    {% set h_value = row[0][-3] %}
                {% endif %}
                {% if has_prediction == true %}
                    {% set a_value = row[0][-2] %}
                {% endif %}
                
                <div class="h">{{h_value}}</div>
                <div class="a">{{a_value}}</div>
            </div>

            <!-- Display existing prediction -->
            {% if has_prediction and prediction_data %}
            <div class="user-prediction">
                <h5>Your Prediction:</h5>
                <ul>
                    {% if prediction_data.winner %}
                    <li><strong>Winner:</strong> 
                        {% if prediction_data.winner == 'player1' %}{{ row[0][1] }}{% else %}{{ row[1][1] }}{% endif %}
                    </li>
                    {% endif %}
                    
                    {% if prediction_data.player1_score or prediction_data.player2_score %}
                    <li><strong>Score:</strong> 
                        {% if prediction_data.player1_score %}{{ row[0][1] }}: {{ prediction_data.player1_score }}{% endif %}
                        {% if prediction_data.player1_score and prediction_data.player2_score %} - {% endif %}
                        {% if prediction_data.player2_score %}{{ row[1][1] }}: {{ prediction_data.player2_score }}{% endif %}
                    </li>
                    {% endif %}
                    
                    {% if prediction_data.spread %}
                    <li><strong>Spread:</strong> {{ prediction_data.spread }}</li>
                    {% endif %}
                    
                    {% if prediction_data.notes %}
                    <li><strong>Notes:</strong> {{ prediction_data.notes }}</li>
                    {% endif %}
                </ul>
                
                <!-- Prediction success banner -->
                <div class="prediction-result-banner success">
                    <span>Prediction Correct!</span>
                </div>
            </div>
            {% endif %}
        </div>
        
        {% endif %}
        
        {% endfor %}
    </div>

    <div class="todayMatches matches_container men">
        {% if today_rows | length == 0 %}
        <div>No data found</div>
        {% endif %}
        
        {% set match_counter = namespace(value=0) %}
        {% for row in today_rows %}
        
        {% if row[-1] == "header" %}
        
        <div class="header">
            {% for i in range(11) %}
            
            <div class="col">{{row[i]}}</div>
            
            {% endfor %}
        </div>
        
        {% else %}
        
        <div class="match-prediction-container">
            <div class="row">
                <div class="time">{{row[0][0]}}</div>
                <div class="players">
                    <div class="player1">
                        {% for j in range(1,9) %}
                        <div>{{row[0][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[0] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="player2">
                        {% for j in range(1,9) %}
                        <div>{{row[1][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[1] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Extract h and a values with a simpler approach -->
                {% set h_value = row[0][-2] %}
                {% set a_value = row[0][-1] %}

                <!-- Check if we have prediction data -->
                {% set has_prediction_men = false %}
                {% set prediction_data = None %}
                
                {% if row[0][-1] is string and "prediction" in row[0][-1].split(":") %}
                    {% set has_prediction_men = true %}
                    {% set prediction_text = row[0][-1][11:] %}
                    {% set prediction_data = prediction_text | fromjson %}
                {% endif %}

                <!-- Set h and a values from the row data -->
                {% if has_prediction_men == true %}
                    {% set h_value = row[0][-3] %}
                {% endif %}
                {% if has_prediction_men == true %}
                    {% set a_value = row[0][-2] %}
                {% endif %}
                
                <div class="h">{{h_value}}</div>
                <div class="a">{{a_value}}</div>
            </div>

            <!-- Single prediction button below the match row -->
            <div class="prediction-bar-container">
                <div class="prediction-bar" onclick="window.togglePredictionForm({{ match_counter.value }}, false)">
                    <span>{% if row|length > 2 and row[2].has_prediction %}Edit Prediction{% else %}Make Prediction{% endif %}</span>
                </div>
            </div>

            <!-- Add prediction form and existing prediction display here -->
            <div class="prediction-form" id="prediction-form-{{ match_counter.value }}-m" style="display: none;">
                <form class="predict-form">
                    <!-- Hidden inputs for match data -->
                    <input type="hidden" name="match_index" value="{{ match_counter.value }}">
                    <input type="hidden" name="is_women" value="false">
                    
                    <!-- For debugging -->
                    <div style="font-size: 10px; color: #999; margin-bottom: 10px;">Match Index: {{ match_counter.value }}</div>
                    
                    <!-- Winner selection -->
                    <div class="form-group">
                        <label>Predict Winner:</label>
                        <select name="winner" required>
                            <option value="">-- Select Winner --</option>
                            <option value="player1" {% if row|length > 2 and row[2].prediction_data.winner == 'player1' %}selected{% endif %}>
                                {{ row[0][1] }}
                            </option>
                            <option value="player2" {% if row|length > 2 and row[2].prediction_data.winner == 'player2' %}selected{% endif %}>
                                {{ row[1][1] }}
                            </option>
                        </select>
                    </div>
                    
                    <!-- Score prediction -->
                    <div class="form-group">
                        <label>Predict Score:</label>
                        <div class="score-input">
                            <input type="text" name="player1_score" placeholder="Player 1 sets"
                                   value="{% if row|length > 2 %}{{ row[2].prediction_data.player1_score }}{% endif %}">
                            <span>-</span>
                            <input type="text" name="player2_score" placeholder="Player 2 sets"
                                   value="{% if row|length > 2 %}{{ row[2].prediction_data.player2_score }}{% endif %}">
                        </div>
                    </div>
                    
                    <!-- Spread prediction -->
                    <div class="form-group">
                        <label>Predict Spread:</label>
                        <input type="number" step="0.1" name="spread" placeholder="Enter spread"
                               value="{% if row|length > 2 %}{{ row[2].prediction_data.spread }}{% endif %}">
                    </div>
                    
                    <!-- Notes -->
                    <div class="form-group">
                        <label>Additional Notes:</label>
                        <textarea name="notes" placeholder="Enter any notes or comments">{% if row|length > 2 %}{{ row[2].prediction_data.notes }}{% endif %}</textarea>
                    </div>
                    
                    <!-- Submit button -->
                    <button type="button" class="save-btn" onclick="window.savePrediction(this.closest('form')); return false;">Save Prediction</button>
                </form>
            </div>

            {% if row|length > 2 and row[2].has_prediction %}
            <div class="user-prediction" id="existing-prediction-{{ match_counter.value }}-m">
                <h5>Your Prediction:</h5>
                <ul>
                    {% if row[2].prediction_data.winner %}
                    <li><strong>Winner:</strong> 
                        {% if row[2].prediction_data.winner == 'player1' %}{{ row[0][1] }}{% else %}{{ row[1][1] }}{% endif %}
                    </li>
                    {% endif %}
                    
                    {% if row[2].prediction_data.player1_score or row[2].prediction_data.player2_score %}
                    <li><strong>Score:</strong> 
                        {% if row[2].prediction_data.player1_score %}{{ row[0][1] }}: {{ row[2].prediction_data.player1_score }}{% endif %}
                        {% if row[2].prediction_data.player1_score and row[2].prediction_data.player2_score %} - {% endif %}
                        {% if row[2].prediction_data.player2_score %}{{ row[1][1] }}: {{ row[2].prediction_data.player2_score }}{% endif %}
                    </li>
                    {% endif %}
                    
                    {% if row[2].prediction_data.spread %}
                    <li><strong>Spread:</strong> {{ row[2].prediction_data.spread }}</li>
                    {% endif %}
                    
                    {% if row[2].prediction_data.notes %}
                    <li><strong>Notes:</strong> {{ row[2].prediction_data.notes }}</li>
                    {% endif %}
                </ul>
            </div>
            {% endif %}
        </div>
        
        {% set match_counter.value = match_counter.value + 1 %}
        {% endif %}
        
        {% endfor %}
    </div>

    <div class="nextMatches matches_container men">
        {% if next_rows | length == 0 %}
        <div>No data found</div>
        {% endif %}

        {% set match_counter = namespace(value=0) %}
        {% for row in next_rows %}
    
        {% if row[-1] == "header" %}
    
        <div class="header">
            {% for i in range(11) %}
            
            <div class="col">{{row[i]}}</div>

            {% endfor %}
        </div>
    
        {% endif %}
    
        {% if row[-1] != "header" %}
    
        <div class="match-prediction-container">
            <div class="row">
                <div class="time">{{row[0][0]}}</div>
                <div class="players">
                    <div class="player1">
                        {% for j in range(1,9) %}
                        <div>{{row[0][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[0] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="player2">
                        {% for j in range(1,9) %}
                        <div>{{row[1][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[1] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Extract h and a values with a simpler approach -->
                {% set h_value = row[0][-2] %}
                {% set a_value = row[0][-1] %}
                
                <!-- Set h and a values from the row data -->
                {% if row|length > 2 and row[2].has_prediction %}
                    {% set h_value = row[0][-3] %}
                    {% set a_value = row[0][-2] %}
                {% endif %}
                
                <div class="h">{{h_value}}</div>
                <div class="a">{{a_value}}</div>
            </div>

            <!-- Single prediction button below the match row -->
            <div class="prediction-bar-container">
                <div class="prediction-bar" onclick="window.togglePredictionForm({{ match_counter.value }}, false, true)">
                    <span>{% if row|length > 2 and row[2].has_prediction %}Edit Prediction{% else %}Make Prediction{% endif %}</span>
                </div>
            </div>

            <!-- Add prediction form -->
            <div class="prediction-form" id="prediction-form-{{ match_counter.value }}-m-next" style="display: none;">
                <form class="predict-form">
                    <!-- Hidden inputs for match data -->
                    <input type="hidden" name="match_index" value="{{ match_counter.value }}">
                    <input type="hidden" name="is_women" value="false">
                    <input type="hidden" name="is_next_day" value="true">
                    
                    <!-- For debugging -->
                    <div style="font-size: 10px; color: #999; margin-bottom: 10px;">Match Index: {{ match_counter.value }}</div>
                    
                    <!-- Winner selection -->
                    <div class="form-group">
                        <label>Predict Winner:</label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="player1-{{ match_counter.value }}-m-next"
                                       name="winner" value="player1"
                                       {% if row|length > 2 and row[2].prediction_data.winner == 'player1' %}checked{% endif %}>
                                <label for="player1-{{ match_counter.value }}-m-next">{{ row[0][1] }}</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="player2-{{ match_counter.value }}-m-next"
                                       name="winner" value="player2"
                                       {% if row|length > 2 and row[2].prediction_data.winner == 'player2' %}checked{% endif %}>
                                <label for="player2-{{ match_counter.value }}-m-next">{{ row[1][1] }}</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Score prediction -->
                    <div class="form-group">
                        <label>Predict Scores (optional):</label>
                        <div class="score-inputs">
                            <div class="score-input">
                                <label>{{ row[0][1] }}:</label>
                                <input type="text" name="player1_score" placeholder="Score"
                                       value="{% if row|length > 2 %}{{ row[2].prediction_data.player1_score }}{% endif %}">
                            </div>
                            <div class="score-input">
                                <label>{{ row[1][1] }}:</label>
                                <input type="text" name="player2_score" placeholder="Score"
                                       value="{% if row|length > 2 %}{{ row[2].prediction_data.player2_score }}{% endif %}">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Spread prediction -->
                    <div class="form-group">
                        <label>Predict Spread:</label>
                        <input type="number" step="0.1" name="spread" placeholder="Enter spread"
                               value="{% if row|length > 2 %}{{ row[2].prediction_data.spread }}{% endif %}">
                    </div>

                    <!-- Notes -->
                    <div class="form-group">
                        <label>Additional Notes:</label>
                        <textarea name="notes" placeholder="Enter any notes or comments">{% if row|length > 2 %}{{ row[2].prediction_data.notes }}{% endif %}</textarea>
                    </div>
                    
                    <!-- Submit button -->
                    <div class="form-actions">
                        <button type="button" class="cancel-btn" onclick="window.togglePredictionForm({{ match_counter.value }}, false, true)">Cancel</button>
                        <button type="button" class="save-btn" onclick="window.savePrediction(this.form)">Save Prediction</button>
                    </div>
                </form>
            </div>

            <!-- Display existing prediction -->
            {% if row|length > 2 and row[2].has_prediction %}
            <div class="user-prediction" id="existing-prediction-{{ match_counter.value }}-m-next">
                <h5>Your Prediction:</h5>
                <ul>
                    {% if row[2].prediction_data.winner %}
                    <li><strong>Winner:</strong>
                        {% if row[2].prediction_data.winner == 'player1' %}{{ row[0][1] }}{% else %}{{ row[1][1] }}{% endif %}
                    </li>
                    {% endif %}

                    {% if row[2].prediction_data.player1_score or row[2].prediction_data.player2_score %}
                    <li><strong>Score:</strong>
                        {% if row[2].prediction_data.player1_score %}{{ row[0][1] }}: {{ row[2].prediction_data.player1_score }}{% endif %}
                        {% if row[2].prediction_data.player1_score and row[2].prediction_data.player2_score %} - {% endif %}
                        {% if row[2].prediction_data.player2_score %}{{ row[1][1] }}: {{ row[2].prediction_data.player2_score }}{% endif %}
                    </li>
                    {% endif %}

                    {% if row[2].prediction_data.spread %}
                    <li><strong>Spread:</strong> {{ row[2].prediction_data.spread }}</li>
                    {% endif %}

                    {% if row[2].prediction_data.notes %}
                    <li><strong>Notes:</strong> {{ row[2].prediction_data.notes }}</li>
                    {% endif %}
                </ul>
            </div>
            {% endif %}
        </div>

        {% set match_counter.value = match_counter.value + 1 %}
        {% endif %}
    
        {% endfor %}
    </div>
    
    <div class="prevMatches matches_container women">
        {% if prev_rows_w | length == 0 %}
        <div>No data found</div>
        {% endif %}
        {% for row in prev_rows_w %}
    
        {% if row[-1] == "header" %}
    
        <div class="header">
            {% for i in range(11) %}
            
            <div class="col">{{row[i]}}</div>
    
            {% endfor %}
        </div>
    
        {% endif %}
        
        {% if row[-1] != "header" %}
    
        <div class="match-prediction-container">
            <div class="row">
                <div class="time">{{row[0][0]}}</div>
                <div class="players">
                    <div class="player1">
                        {% for j in range(1,9) %}
                        <div>{{row[0][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[0] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="player2">
                        {% for j in range(1,9) %}
                        <div>{{row[1][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[1] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Extract h and a values with a simpler approach -->
                {% set h_value = row[0][-2] %}
                {% set a_value = row[0][-1] %}
                
                <!-- Check if we have prediction data -->
                {% set has_prediction = false %}
                {% set prediction_data = None %}
                
                {% if row[0][-1] is string and "prediction" in row[0][-1].split(":") %}
                    {% set has_prediction = true %}
                    {% set prediction_text = row[0][-1][11:] %}
                    {% set prediction_data = prediction_text | fromjson %}
                {% endif %}
                
                <!-- Set h and a values from the row data -->
                {% if has_prediction == true %}
                    {% set h_value = row[0][-3] %}
                {% endif %}
                {% if has_prediction == true %}
                    {% set a_value = row[0][-2] %}
                {% endif %}
                
                <div class="h">{{h_value}}</div>
                <div class="a">{{a_value}}</div>
            </div>

            <!-- Display existing prediction -->
            {% if has_prediction and prediction_data %}
            <div class="user-prediction">
                <h5>Your Prediction:</h5>
                <ul>
                    {% if prediction_data.winner %}
                    <li><strong>Winner:</strong> 
                        {% if prediction_data.winner == 'player1' %}{{ row[0][1] }}{% else %}{{ row[1][1] }}{% endif %}
                    </li>
                    {% endif %}
                    
                    {% if prediction_data.player1_score or prediction_data.player2_score %}
                    <li><strong>Score:</strong> 
                        {% if prediction_data.player1_score %}{{ row[0][1] }}: {{ prediction_data.player1_score }}{% endif %}
                        {% if prediction_data.player1_score and prediction_data.player2_score %} - {% endif %}
                        {% if prediction_data.player2_score %}{{ row[1][1] }}: {{ prediction_data.player2_score }}{% endif %}
                    </li>
                    {% endif %}
                    
                    {% if prediction_data.spread %}
                    <li><strong>Spread:</strong> {{ prediction_data.spread }}</li>
                    {% endif %}
                    
                    {% if prediction_data.notes %}
                    <li><strong>Notes:</strong> {{ prediction_data.notes }}</li>
                    {% endif %}
                </ul>
                
                <!-- Prediction success banner -->
                <div class="prediction-result-banner success">
                    <span>Prediction Correct!</span>
                </div>
            </div>
            {% endif %}
        </div>
    
        {% endif %}
    
        {% endfor %}
    </div>

    <div class="todayMatches matches_container women">
        {% if today_rows_w | length == 0 %}
        <div>No data found</div>
        {% endif %}
        
        {% set match_counter = namespace(value=0) %}
        {% for row in today_rows_w %}
        
        {% if row[-1] == "header" %}
        
        <div class="header">
            {% for i in range(11) %}
            
            <div class="col">{{row[i]}}</div>
            
            {% endfor %}
        </div>
        
        {% else %}
        
        <div class="match-prediction-container">
            <div class="row">
                <div class="time">{{row[0][0]}}</div>
                <div class="players">
                    <div class="player1">
                        {% for j in range(1,9) %}
                        <div>{{row[0][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[0] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="player2">
                        {% for j in range(1,9) %}
                        <div>{{row[1][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[1] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Extract h and a values with a simpler approach -->
                {% set h_value = row[0][-2] %}
                {% set a_value = row[0][-1] %}
                
                <!-- Check if we have prediction data -->
                {% set has_prediction_women = false %}
                {% set prediction_data = None %}
                
                {% if row[0][-1] is string and "prediction" in row[0][-1].split(":") %}
                    {% set has_prediction_women = true %}
                    {% set prediction_text = row[0][-1][11:] %}
                    {% set prediction_data = prediction_text | fromjson %}
                {% endif %}
                
                <!-- Set h and a values from the row data -->
                {% if has_prediction_women == true %}
                    {% set h_value = row[0][-3] %}
                {% endif %}
                {% if has_prediction_women == true %}
                    {% set a_value = row[0][-2] %}
                {% endif %}
                
                <div class="h">{{h_value}}</div>
                <div class="a">{{a_value}}</div>
            </div>

            <!-- Single prediction button below the match row -->
            <div class="prediction-bar-container">
                <div class="prediction-bar" onclick="window.togglePredictionForm({{ match_counter.value }}, true)">
                    <span>{% if row|length > 2 and row[2].has_prediction %}Edit Prediction{% else %}Make Prediction{% endif %}</span>
                </div>
            </div>

            <!-- Add prediction form and existing prediction display here -->
            <div class="prediction-form" id="prediction-form-{{ match_counter.value }}-w" style="display: none;">
                <form class="predict-form">
                    <!-- Hidden inputs for match data -->
                    <input type="hidden" name="match_index" value="{{ match_counter.value }}">
                    <input type="hidden" name="is_women" value="true">
                    
                    <!-- For debugging -->
                    <div style="font-size: 10px; color: #999; margin-bottom: 10px;">Match Index: {{ match_counter.value }}</div>
                    
                    <!-- Winner selection -->
                    <div class="form-group">
                        <label>Predict Winner:</label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="player1-{{ match_counter.value }}-w" 
                                       name="winner" value="player1"
                                       {% if row|length > 2 and row[2].prediction_data.winner == 'player1' %}checked{% endif %}>
                                <label for="player1-{{ match_counter.value }}-w">{{ row[0][1] }}</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="player2-{{ match_counter.value }}-w" 
                                       name="winner" value="player2"
                                       {% if row|length > 2 and row[2].prediction_data.winner == 'player2' %}checked{% endif %}>
                                <label for="player2-{{ match_counter.value }}-w">{{ row[1][1] }}</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Score prediction -->
                    <div class="form-group">
                        <label>Predict Scores (optional):</label>
                        <div class="score-inputs">
                            <div class="score-input">
                                <label>{{ row[0][1] }}:</label>
                                <input type="text" name="player1_score" placeholder="Score" 
                                       value="{% if row|length > 2 %}{{ row[2].prediction_data.player1_score }}{% endif %}">
                            </div>
                            <div class="score-input">
                                <label>{{ row[1][1] }}:</label>
                                <input type="text" name="player2_score" placeholder="Score"
                                       value="{% if row|length > 2 %}{{ row[2].prediction_data.player2_score }}{% endif %}">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Spread prediction -->
                    <div class="form-group">
                        <label>Predict Spread:</label>
                        <input type="number" step="0.1" name="spread" placeholder="Enter spread"
                               value="{% if row|length > 2 %}{{ row[2].prediction_data.spread }}{% endif %}">
                    </div>
                    
                    <!-- Notes -->
                    <div class="form-group">
                        <label>Additional Notes:</label>
                        <textarea name="notes" placeholder="Enter any notes or comments">{% if row|length > 2 %}{{ row[2].prediction_data.notes }}{% endif %}</textarea>
                    </div>
                    
                    <!-- Submit button -->
                    <button type="button" class="save-btn" onclick="window.savePrediction(this.closest('form')); return false;">Save Prediction</button>
                </form>
            </div>
            
            {% if row|length > 2 and row[2].has_prediction %}
            <div class="user-prediction" id="existing-prediction-{{ match_counter.value }}-w">
                <h5>Your Prediction:</h5>
                <ul>
                    {% if row[2].prediction_data.winner %}
                    <li><strong>Winner:</strong> 
                        {% if row[2].prediction_data.winner == 'player1' %}{{ row[0][1] }}{% else %}{{ row[1][1] }}{% endif %}
                    </li>
                    {% endif %}
                    
                    {% if row[2].prediction_data.player1_score or row[2].prediction_data.player2_score %}
                    <li><strong>Score:</strong> 
                        {% if row[2].prediction_data.player1_score %}{{ row[0][1] }}: {{ row[2].prediction_data.player1_score }}{% endif %}
                        {% if row[2].prediction_data.player1_score and row[2].prediction_data.player2_score %} - {% endif %}
                        {% if row[2].prediction_data.player2_score %}{{ row[1][1] }}: {{ row[2].prediction_data.player2_score }}{% endif %}
                    </li>
                    {% endif %}
                    
                    {% if row[2].prediction_data.spread %}
                    <li><strong>Spread:</strong> {{ row[2].prediction_data.spread }}</li>
                    {% endif %}
                    
                    {% if row[2].prediction_data.notes %}
                    <li><strong>Notes:</strong> {{ row[2].prediction_data.notes }}</li>
                    {% endif %}
                </ul>
            </div>
            {% endif %}
        </div>
        
        {% set match_counter.value = match_counter.value + 1 %}
        {% endif %}
        
        {% endfor %}
    </div>

    <div class="nextMatches matches_container women">
        {% if next_rows_w | length == 0 %}
        <div>No data found</div>
        {% endif %}
        
        {% set match_counter = namespace(value=0) %}
        {% for row in next_rows_w %}
    
        {% if row[-1] == "header" %}
    
        <div class="header">
            {% for i in range(11) %}
            
            <div class="col">{{row[i]}}</div>
            
            {% endfor %}
        </div>
        
        {% endif %}
        
        {% if row[-1] != "header" %}
        
        <div class="match-prediction-container">
            <div class="row">
                <div class="time">{{row[0][0]}}</div>
                <div class="players">
                    <div class="player1">
                        {% for j in range(1,9) %}
                        <div>{{row[0][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[0] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <div class="player2">
                        {% for j in range(1,9) %}
                        <div>{{row[1][j]}}</div>
                        {% endfor %}
                        
                        <!-- Add prediction data as a hidden div -->
                        {% for item in row[1] %}
                            {% if item is string and 'prediction:' in item %}
                            <div class="prediction-data" style="display:none;">{{item}}</div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Extract h and a values with a simpler approach -->
                {% set h_value = row[0][-2] %}
                {% set a_value = row[0][-1] %}
                
                <!-- Set h and a values from the row data -->
                {% if row|length > 2 and row[2].has_prediction %}
                    {% set h_value = row[0][-3] %}
                    {% set a_value = row[0][-2] %}
                {% endif %}
                
                <div class="h">{{h_value}}</div>
                <div class="a">{{a_value}}</div>
            </div>

            <!-- Single prediction button below the match row -->
            <div class="prediction-bar-container">
                <div class="prediction-bar" onclick="window.togglePredictionForm({{ match_counter.value }}, true, true)">
                    <span>{% if row|length > 2 and row[2].has_prediction %}Edit Prediction{% else %}Make Prediction{% endif %}</span>
                </div>
            </div>

            <!-- Add prediction form -->
            <div class="prediction-form" id="prediction-form-{{ match_counter.value }}-w-next" style="display: none;">
                <form class="predict-form">
                    <!-- Hidden inputs for match data -->
                    <input type="hidden" name="match_index" value="{{ match_counter.value }}">
                    <input type="hidden" name="is_women" value="true">
                    <input type="hidden" name="is_next_day" value="true">
                    
                    <!-- For debugging -->
                    <div style="font-size: 10px; color: #999; margin-bottom: 10px;">Match Index: {{ match_counter.value }}</div>
                    
                    <!-- Winner selection -->
                    <div class="form-group">
                        <label>Predict Winner:</label>
                        <div class="radio-group">
                            <div class="radio-option">
                                <input type="radio" id="player1-{{ match_counter.value }}-w-next"
                                       name="winner" value="player1"
                                       {% if row|length > 2 and row[2].prediction_data.winner == 'player1' %}checked{% endif %}>
                                <label for="player1-{{ match_counter.value }}-w-next">{{ row[0][1] }}</label>
                            </div>
                            <div class="radio-option">
                                <input type="radio" id="player2-{{ match_counter.value }}-w-next"
                                       name="winner" value="player2"
                                       {% if row|length > 2 and row[2].prediction_data.winner == 'player2' %}checked{% endif %}>
                                <label for="player2-{{ match_counter.value }}-w-next">{{ row[1][1] }}</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Score prediction -->
                    <div class="form-group">
                        <label>Predict Scores (optional):</label>
                        <div class="score-inputs">
                            <div class="score-input">
                                <label>{{ row[0][1] }}:</label>
                                <input type="text" name="player1_score" placeholder="Score"
                                       value="{% if row|length > 2 %}{{ row[2].prediction_data.player1_score }}{% endif %}">
                            </div>
                            <div class="score-input">
                                <label>{{ row[1][1] }}:</label>
                                <input type="text" name="player2_score" placeholder="Score"
                                       value="{% if row|length > 2 %}{{ row[2].prediction_data.player2_score }}{% endif %}">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Spread prediction -->
                    <div class="form-group">
                        <label>Predict Spread:</label>
                        <input type="number" step="0.1" name="spread" placeholder="Enter spread"
                               value="{% if row|length > 2 %}{{ row[2].prediction_data.spread }}{% endif %}">
                    </div>

                    <!-- Notes -->
                    <div class="form-group">
                        <label>Additional Notes:</label>
                        <textarea name="notes" placeholder="Enter any notes or comments">{% if row|length > 2 %}{{ row[2].prediction_data.notes }}{% endif %}</textarea>
                    </div>
                    
                    <!-- Submit button -->
                    <div class="form-actions">
                        <button type="button" class="cancel-btn" onclick="window.togglePredictionForm({{ match_counter.value }}, true, true)">Cancel</button>
                        <button type="button" class="save-btn" onclick="window.savePrediction(this.form)">Save Prediction</button>
                    </div>
                </form>
            </div>

            <!-- Display existing prediction -->
            {% if row|length > 2 and row[2].has_prediction %}
            <div class="user-prediction" id="existing-prediction-{{ match_counter.value }}-w-next">
                <h5>Your Prediction:</h5>
                <ul>
                    {% if row[2].prediction_data.winner %}
                    <li><strong>Winner:</strong>
                        {% if row[2].prediction_data.winner == 'player1' %}{{ row[0][1] }}{% else %}{{ row[1][1] }}{% endif %}
                    </li>
                    {% endif %}

                    {% if row[2].prediction_data.player1_score or row[2].prediction_data.player2_score %}
                    <li><strong>Score:</strong>
                        {% if row[2].prediction_data.player1_score %}{{ row[0][1] }}: {{ row[2].prediction_data.player1_score }}{% endif %}
                        {% if row[2].prediction_data.player1_score and row[2].prediction_data.player2_score %} - {% endif %}
                        {% if row[2].prediction_data.player2_score %}{{ row[1][1] }}: {{ row[2].prediction_data.player2_score }}{% endif %}
                    </li>
                    {% endif %}

                    {% if row[2].prediction_data.spread %}
                    <li><strong>Spread:</strong> {{ row[2].prediction_data.spread }}</li>
                    {% endif %}

                    {% if row[2].prediction_data.notes %}
                    <li><strong>Notes:</strong> {{ row[2].prediction_data.notes }}</li>
                    {% endif %}
                </ul>
            </div>
            {% endif %}
        </div>

        {% set match_counter.value = match_counter.value + 1 %}
        {% endif %}
        
        {% endfor %}
    </div>
</body>
</html>
